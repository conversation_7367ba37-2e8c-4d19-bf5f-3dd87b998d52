/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   globals.c                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/04 14:40:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/10 19:34:35 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../include/kfs.h"

/* ──────────── Main <PERSON> ──────────── */
/* All kernel state is now contained within this single structure */
t_kernel kernel = {0};

/* ──────────── Kernel Initialization Function ──────────── */
void kernel_globals_init(void) {
    /* Initialize other pointers to NULL (already done by {0} but explicit for clarity) */
    kernel.terminal_buffer = NULL;
    kernel.kernel_directory = NULL;
    kernel.current_directory = NULL;

    /* Initialize counters and indices */
    kernel.buffer_pos = 0;
    kernel.stack_pointer = 0;
    kernel.screen_index = 0;
    kernel.terminal_ctrl = 0;
    kernel.terminal_shift = 0;

    /* Note: GDT pointer will be initialized in gdt_install() function */
}
