/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   isr.c                                              :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/25 02:10:22 by zerrino           #+#    #+#             */
/*   Updated: 2025/07/10 16:43:12 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kfs.h"

const char *get_exception_message(uint32_t exception_num)
{
    switch (exception_num) {
        case EXCEPTION_DIVIDE_BY_ZERO:
            return EXCEPTION_MSG_DIVIDE_BY_ZERO;
        case EXCEPTION_DEBUG:
            return EXCEPTION_MSG_DEBUG;
        case EXCEPTION_NMI:
            return EXCEPTION_MSG_NMI;
        case EXCEPTION_BREAKPOINT:
            return EXCEPTION_MSG_BREAKPOINT;
        case EXCEPTION_OVERFLOW:
            return EXCEPTION_MSG_OVERFLOW;
        case EXCEPTION_BOUND_RANGE:
            return EXCEPTION_MSG_BOUND_RANGE;
        case EXCEPTION_INVALID_OPCODE:
            return EXCEPTION_MSG_INVALID_OPCODE;
        case EXCEPTION_DEVICE_NOT_AVAILABLE:
            return EXCEPTION_MSG_DEVICE_NOT_AVAILABLE;
        case EXCEPTION_DOUBLE_FAULT:
            return EXCEPTION_MSG_DOUBLE_FAULT;
        case EXCEPTION_COPROCESSOR_OVERRUN:
            return EXCEPTION_MSG_COPROCESSOR_OVERRUN;
        case EXCEPTION_INVALID_TSS:
            return EXCEPTION_MSG_INVALID_TSS;
        case EXCEPTION_SEGMENT_NOT_PRESENT:
            return EXCEPTION_MSG_SEGMENT_NOT_PRESENT;
        case EXCEPTION_STACK_SEGMENT_FAULT:
            return EXCEPTION_MSG_STACK_SEGMENT_FAULT;
        case EXCEPTION_GENERAL_PROTECTION:
            return EXCEPTION_MSG_GENERAL_PROTECTION;
        case EXCEPTION_PAGE_FAULT:
            return EXCEPTION_MSG_PAGE_FAULT;
        case EXCEPTION_X87_FPU:
            return EXCEPTION_MSG_X87_FPU;
        case EXCEPTION_ALIGNMENT_CHECK:
            return EXCEPTION_MSG_ALIGNMENT_CHECK;
        case EXCEPTION_MACHINE_CHECK:
            return EXCEPTION_MSG_MACHINE_CHECK;
        case EXCEPTION_SIMD_FPU:
            return EXCEPTION_MSG_SIMD_FPU;
        case EXCEPTION_VIRTUALIZATION:
            return EXCEPTION_MSG_VIRTUALIZATION;
        case EXCEPTION_CONTROL_PROTECTION:
            return EXCEPTION_MSG_CONTROL_PROTECTION;
        case EXCEPTION_HYPERVISOR_INJECTION:
            return EXCEPTION_MSG_HYPERVISOR_INJECTION;
        case EXCEPTION_VMM_COMMUNICATION:
            return EXCEPTION_MSG_VMM_COMMUNICATION;
        case EXCEPTION_SECURITY:
            return EXCEPTION_MSG_SECURITY;
        case EXCEPTION_RESERVED_15:
        case EXCEPTION_RESERVED_22:
        case EXCEPTION_RESERVED_23:
        case EXCEPTION_RESERVED_24:
        case EXCEPTION_RESERVED_25:
        case EXCEPTION_RESERVED_26:
        case EXCEPTION_RESERVED_27:
        case EXCEPTION_RESERVED_31:
        default:
            return EXCEPTION_MSG_RESERVED;
    }
}

/* Array of ISR function pointers for loop-based initialization */
static void (*isr_functions[256])(void) = {
    ISR0,   ISR1,   ISR2,   ISR3,   ISR4,   ISR5,   ISR6,   ISR7,
    ISR8,   ISR9,   ISR10,  ISR11,  ISR12,  ISR13,  ISR14,  ISR15,
    ISR16,  ISR17,  ISR18,  ISR19,  ISR20,  ISR21,  ISR22,  ISR23,
    ISR24,  ISR25,  ISR26,  ISR27,  ISR28,  ISR29,  ISR30,  ISR31,
    ISR32,  ISR33,  ISR34,  ISR35,  ISR36,  ISR37,  ISR38,  ISR39,
    ISR40,  ISR41,  ISR42,  ISR43,  ISR44,  ISR45,  ISR46,  ISR47,
    ISR48,  ISR49,  ISR50,  ISR51,  ISR52,  ISR53,  ISR54,  ISR55,
    ISR56,  ISR57,  ISR58,  ISR59,  ISR60,  ISR61,  ISR62,  ISR63,
    ISR64,  ISR65,  ISR66,  ISR67,  ISR68,  ISR69,  ISR70,  ISR71,
    ISR72,  ISR73,  ISR74,  ISR75,  ISR76,  ISR77,  ISR78,  ISR79,
    ISR80,  ISR81,  ISR82,  ISR83,  ISR84,  ISR85,  ISR86,  ISR87,
    ISR88,  ISR89,  ISR90,  ISR91,  ISR92,  ISR93,  ISR94,  ISR95,
    ISR96,  ISR97,  ISR98,  ISR99,  ISR100, ISR101, ISR102, ISR103,
    ISR104, ISR105, ISR106, ISR107, ISR108, ISR109, ISR110, ISR111,
    ISR112, ISR113, ISR114, ISR115, ISR116, ISR117, ISR118, ISR119,
    ISR120, ISR121, ISR122, ISR123, ISR124, ISR125, ISR126, ISR127,
    ISR128, ISR129, ISR130, ISR131, ISR132, ISR133, ISR134, ISR135,
    ISR136, ISR137, ISR138, ISR139, ISR140, ISR141, ISR142, ISR143,
    ISR144, ISR145, ISR146, ISR147, ISR148, ISR149, ISR150, ISR151,
    ISR152, ISR153, ISR154, ISR155, ISR156, ISR157, ISR158, ISR159,
    ISR160, ISR161, ISR162, ISR163, ISR164, ISR165, ISR166, ISR167,
    ISR168, ISR169, ISR170, ISR171, ISR172, ISR173, ISR174, ISR175,
    ISR176, ISR177, ISR178, ISR179, ISR180, ISR181, ISR182, ISR183,
    ISR184, ISR185, ISR186, ISR187, ISR188, ISR189, ISR190, ISR191,
    ISR192, ISR193, ISR194, ISR195, ISR196, ISR197, ISR198, ISR199,
    ISR200, ISR201, ISR202, ISR203, ISR204, ISR205, ISR206, ISR207,
    ISR208, ISR209, ISR210, ISR211, ISR212, ISR213, ISR214, ISR215,
    ISR216, ISR217, ISR218, ISR219, ISR220, ISR221, ISR222, ISR223,
    ISR224, ISR225, ISR226, ISR227, ISR228, ISR229, ISR230, ISR231,
    ISR232, ISR233, ISR234, ISR235, ISR236, ISR237, ISR238, ISR239,
    ISR240, ISR241, ISR242, ISR243, ISR244, ISR245, ISR246, ISR247,
    ISR248, ISR249, ISR250, ISR251, ISR252, ISR253, ISR254, ISR255
};

void	ISR_InitializeGates()
{
    int i;

    /* Initialize all 256 ISR gates using a loop */
    for (i = 0; i < 256; i++) {
        IDT_SetGate(i, isr_functions[i], GDT_KERNEL_CODE, IDT_FLAG_RIN0 | IDT_FLAG_GATE_32BIT_INT);
    }

}

void	ISR_Initialize()
{
	ISR_InitializeGates();
	for (int i = 0; i < 256; i++)
		IDT_EnableGate(i);
}


void	__attribute__((cdecl)) ISR_Handler(t_registers* regs)
{
	if (kernel.ISRhandlers[regs->interrupt] != NULL)
		kernel.ISRhandlers[regs->interrupt](regs);
	else if (32 <= regs->interrupt)
	{
		terminal_writestring("Unhandled interrupt : ");
		printnbr(regs->interrupt, 10);
		terminal_writestring("\n");
	}
	else
	{
		terminal_writestring("Unhandled exception : ");
		printnbr(regs->interrupt, 10);
		terminal_writestring("  ");
		terminal_writestring(get_exception_message(regs->interrupt));
		terminal_writestring("\nKERNEL PANIC!\n");
		kernelPanic();
	}
}

void ISR_RegisterHandler(int interrupt, ISRHandler handler)
{
	kernel.ISRhandlers[interrupt] = handler;
	IDT_EnableGate(interrupt);
}
