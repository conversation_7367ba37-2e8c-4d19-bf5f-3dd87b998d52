/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kfs3.h                                             :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/10 12:15:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/10 12:15:12 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef KFS3_FUNCTIONS_H
# define KFS3_FUNCTIONS_H

#include "../core/structs.h"

/* ──────────── KFS3 Memory Test Functions ──────────── */
void        handle_memstats(void);
void        handle_memtest(void);
void        handle_memtest1(void);
void        handle_memtest2(void);
void        handle_memtest3(void);
void        handle_memtest4(void);
void        handle_memtest5(void);
void        handle_vmemstats(void);
void        handle_memcheck(void);

/* ──────────── KFS3 Crash Test Functions ──────────── */
void        handle_crashtest(void);
void        handle_crash1(void);
void        handle_crash2(void);
void        handle_crash3(void);
void        handle_crash4(void);
void        handle_crash5(void);
void        handle_crash6(void);

/* ──────────── KFS3 Command Handler Functions ──────────── */
command_type_t get_kfs3_memory_command_type(const char* command);
command_type_t get_kfs3_crash_command_type(const char* command);
int         handle_kfs3_memory_commands(command_type_t cmd_type, const char* arg);
int         handle_kfs3_crash_commands(command_type_t cmd_type, const char* arg);

#endif /* KFS3_FUNCTIONS_H */
